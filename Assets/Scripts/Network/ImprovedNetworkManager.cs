﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using CustomNetworking.Core;
using GooseDuckKill.Network.Models;

namespace GooseDuckKill.Network
{
    /// <summary>
    /// 改进的网络管理器 - 集成统一的网络配置和后台服务
    /// 确保前台与后台服务的完全一致性
    /// </summary>
    public class ImprovedNetworkManager : MonoBehaviour
    {
        [Header("Network Settings")]
        [SerializeField] private int maxPlayers = 16;
        [SerializeField] private bool useBackendServices = true;
        [SerializeField] private bool enableDebugLogs = true;
        
        [Header("Components")]
        [SerializeField] private RoomManager roomManager;
        [SerializeField] private PlayerSpawner playerSpawner;
        [SerializeField] private NetworkPrefabRef playerPrefab;
        
        // 网络组件
        private NetworkRunner _runner;
        private NetworkServiceManager _serviceManager;
        private AuthManager _authManager;
        private ImprovedWebSocketManager _webSocketManager;
        
        // 连接状态
        private bool _isConnecting;
        private bool _isConnected;
        private string _currentRoomId;
        
        // 单例实例
        private static ImprovedNetworkManager _instance;
        
        // 事件
        public event Action OnConnectedToServerEvent;
        public event Action<string> OnDisconnectedFromServerEvent;
        public event Action<string> OnRoomJoined;
        public event Action<string> OnRoomLeft;
        #pragma warning disable CS0067 // Event is never used
        public event Action<List<Room>> OnRoomListUpdated;
        #pragma warning restore CS0067
        public event Action<string> OnErrorOccurred;
        
        /// <summary>
        /// 单例访问器
        /// </summary>
        public static ImprovedNetworkManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindFirstObjectByType<ImprovedNetworkManager>();

                    if (_instance == null)
                    {
                        GameObject obj = new GameObject("ImprovedNetworkManager");
                        _instance = obj.AddComponent<ImprovedNetworkManager>();
                    }
                }
                
                return _instance;
            }
        }
        
        /// <summary>
        /// 获取NetworkRunner实例
        /// </summary>
        public NetworkRunner Runner => _runner;
        
        /// <summary>
        /// 获取当前连接状态
        /// </summary>
        public bool IsConnected => _isConnected;
        
        /// <summary>
        /// 获取正在连接状态
        /// </summary>
        public bool IsConnecting => _isConnecting;
        
        /// <summary>
        /// 获取当前房间ID
        /// </summary>
        public string CurrentRoomId => _currentRoomId;
        
        private void Awake()
        {
            // 确保单例
            if (_instance != null && _instance != this)
            {
                Destroy(gameObject);
                return;
            }
            
            _instance = this;
            DontDestroyOnLoad(gameObject);
            
            InitializeComponents();
        }
        
        private void Start()
        {
            InitializeNetworkServices();
        }
        
        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            // 获取或创建NetworkServiceManager
            _serviceManager = NetworkServiceManager.Instance;
            
            // 获取或创建AuthManager
            _authManager = FindFirstObjectByType<AuthManager>();
            if (_authManager == null)
            {
                var authGO = new GameObject("AuthManager");
                authGO.transform.SetParent(transform);
                _authManager = authGO.AddComponent<AuthManager>();
            }

            // 获取或创建WebSocketManager
            _webSocketManager = FindFirstObjectByType<ImprovedWebSocketManager>();
            if (_webSocketManager == null)
            {
                var wsGO = new GameObject("WebSocketManager");
                wsGO.transform.SetParent(transform);
                _webSocketManager = wsGO.AddComponent<ImprovedWebSocketManager>();
            }
            
            // 初始化其他组件
            if (roomManager == null)
            {
                roomManager = GetComponentInChildren<RoomManager>();
            }
            
            if (playerSpawner == null)
            {
                playerSpawner = GetComponentInChildren<PlayerSpawner>();
            }
        }
        
        /// <summary>
        /// 初始化网络服务
        /// </summary>
        private void InitializeNetworkServices()
        {
            if (useBackendServices)
            {
                // 订阅WebSocket事件
                _webSocketManager.OnConnected += HandleWebSocketConnected;
                _webSocketManager.OnDisconnected += HandleWebSocketDisconnected;
                _webSocketManager.OnRoomJoined += HandleRoomJoined;
                _webSocketManager.OnRoomLeft += HandleRoomLeft;
                _webSocketManager.OnError += HandleWebSocketError;
                
                // 订阅认证事件
                _authManager.OnAuthStateChanged += HandleAuthStateChanged;
                _authManager.OnAuthError += HandleAuthError;
                
                Log("Network services initialized with backend integration.");
            }
            else
            {
                Log("Network services initialized in standalone mode.");
            }
        }
        
        /// <summary>
        /// 连接到服务器
        /// </summary>
        public async Task<bool> ConnectToServerAsync()
        {
            if (_isConnecting) return false;
            
            _isConnecting = true;
            
            try
            {
                if (useBackendServices)
                {
                    // 使用后台服务连接
                    return await ConnectToBackendServicesAsync();
                }
                else
                {
                    // 使用自定义网络框架连接
                    return await ConnectToCustomNetworkAsync();
                }
            }
            catch (Exception ex)
            {
                LogError($"Failed to connect to server: {ex.Message}");
                OnErrorOccurred?.Invoke($"连接服务器失败: {ex.Message}");
                return false;
            }
            finally
            {
                _isConnecting = false;
            }
        }
        
        /// <summary>
        /// 连接到后台服务
        /// </summary>
        private async Task<bool> ConnectToBackendServicesAsync()
        {
            Log("Connecting to backend services...");
            
            // 检查认证状态
            if (!_authManager.IsAuthenticated)
            {
                OnErrorOccurred?.Invoke("请先登录");
                return false;
            }
            
            // 连接WebSocket
            _webSocketManager.ConnectToServer();
            
            // 等待连接完成
            float timeout = NetworkConfig.Instance.WebSocketTimeout;
            float elapsed = 0f;
            
            while (!_webSocketManager.IsConnected && elapsed < timeout)
            {
                await Task.Delay(100);
                elapsed += 0.1f;
            }
            
            if (_webSocketManager.IsConnected)
            {
                _isConnected = true;
                OnConnectedToServerEvent?.Invoke();
                Log("Successfully connected to backend services.");
                return true;
            }
            else
            {
                OnErrorOccurred?.Invoke("连接WebSocket超时");
                return false;
            }
        }
        
        /// <summary>
        /// 连接到自定义网络
        /// </summary>
        private async Task<bool> ConnectToCustomNetworkAsync()
        {
            Log("Connecting to custom network...");
            
            if (_runner == null)
            {
                _runner = gameObject.AddComponent<NetworkRunner>();
            }
            
            var startGameArgs = new StartGameArgs
            {
                GameMode = NetworkRunner.GameMode.AutoHostOrClient,
                SessionName = "AutoRoom",
                MaxPlayers = maxPlayers
            };
            
            var result = await _runner.StartGame(startGameArgs);
            if (result.Ok)
            {
                _isConnected = true;
                OnConnectedToServerEvent?.Invoke();
                Log("Successfully connected to custom network.");
                return true;
            }
            else
            {
                OnErrorOccurred?.Invoke($"自定义网络连接失败: {result.ErrorMessage}");
                return false;
            }
        }
        
        /// <summary>
        /// 创建房间
        /// </summary>
        public async Task<bool> CreateRoomAsync(string roomName, string password = "", RoomSettings settings = null)
        {
            if (!_isConnected)
            {
                OnErrorOccurred?.Invoke("未连接到服务器");
                return false;
            }
            
            if (useBackendServices)
            {
                return await CreateRoomOnBackendAsync(roomName, password, settings);
            }
            else
            {
                return await CreateRoomOnCustomNetworkAsync(roomName);
            }
        }
        
        /// <summary>
        /// 在后台服务创建房间
        /// </summary>
        private async Task<bool> CreateRoomOnBackendAsync(string roomName, string password, RoomSettings settings)
        {
            var request = new CreateRoomRequest
            {
                name = roomName,
                max_players = maxPlayers,
                is_private = !string.IsNullOrEmpty(password),
                password = password,
                settings = settings ?? new RoomSettings()
            };
            
            var tcs = new TaskCompletionSource<bool>();
            
            _serviceManager.SendPostRequest<Room>(ServiceType.Room, "/rooms", request, true, response =>
            {
                if (response.IsSuccess)
                {
                    _currentRoomId = response.data.id;
                    Log($"Room created successfully: {response.data.id}");

                    // 通过WebSocket加入房间
                    _webSocketManager.JoinRoom(response.data.id, password);
                    tcs.SetResult(true);
                }
                else
                {
                    LogError($"Failed to create room: {response.message}");
                    OnErrorOccurred?.Invoke($"创建房间失败: {response.message}");
                    tcs.SetResult(false);
                }
            });
            
            return await tcs.Task;
        }
        
        /// <summary>
        /// 在自定义网络创建房间
        /// </summary>
        private async Task<bool> CreateRoomOnCustomNetworkAsync(string roomName)
        {
            if (_runner.IsRunning)
            {
                await _runner.Shutdown();
            }
            
            var startGameArgs = new StartGameArgs
            {
                GameMode = NetworkRunner.GameMode.Host,
                SessionName = roomName,
                MaxPlayers = maxPlayers
            };
            
            var result = await _runner.StartGame(startGameArgs);
            if (result.Ok)
            {
                _currentRoomId = roomName;
                OnRoomJoined?.Invoke(roomName);
                Log($"Custom network room created: {roomName}");
                return true;
            }
            else
            {
                OnErrorOccurred?.Invoke($"创建自定义网络房间失败: {result.ErrorMessage}");
                return false;
            }
        }
        
        /// <summary>
        /// 加入房间
        /// </summary>
        public async Task<bool> JoinRoomAsync(string roomId, string password = "")
        {
            if (!_isConnected)
            {
                OnErrorOccurred?.Invoke("未连接到服务器");
                return false;
            }
            
            if (useBackendServices)
            {
                return await JoinRoomOnBackendAsync(roomId, password);
            }
            else
            {
                return await JoinRoomOnCustomNetworkAsync(roomId);
            }
        }
        
        /// <summary>
        /// 在后台服务加入房间
        /// </summary>
        private async Task<bool> JoinRoomOnBackendAsync(string roomId, string password)
        {
            var request = new JoinRoomRequest
            {
                password = password
            };
            
            var tcs = new TaskCompletionSource<bool>();
            
            _serviceManager.SendPostRequest<object>(ServiceType.Room, $"/rooms/{roomId}/join", request, true, response =>
            {
                if (response.IsSuccess)
                {
                    _currentRoomId = roomId;
                    Log($"Joined room successfully: {roomId}");
                    
                    // 通过WebSocket加入房间
                    _webSocketManager.JoinRoom(roomId, password);
                    tcs.SetResult(true);
                }
                else
                {
                    LogError($"Failed to join room: {response.message}");
                    OnErrorOccurred?.Invoke($"加入房间失败: {response.message}");
                    tcs.SetResult(false);
                }
            });
            
            return await tcs.Task;
        }
        
        /// <summary>
        /// 在自定义网络加入房间
        /// </summary>
        private async Task<bool> JoinRoomOnCustomNetworkAsync(string roomId)
        {
            if (_runner.IsRunning)
            {
                await _runner.Shutdown();
            }
            
            var startGameArgs = new StartGameArgs
            {
                GameMode = NetworkRunner.GameMode.Client,
                SessionName = roomId,
                MaxPlayers = maxPlayers
            };
            
            var result = await _runner.StartGame(startGameArgs);
            if (result.Ok)
            {
                _currentRoomId = roomId;
                OnRoomJoined?.Invoke(roomId);
                Log($"Joined custom network room: {roomId}");
                return true;
            }
            else
            {
                OnErrorOccurred?.Invoke($"加入自定义网络房间失败: {result.ErrorMessage}");
                return false;
            }
        }
        
        /// <summary>
        /// 离开房间
        /// </summary>
        public async Task LeaveRoomAsync()
        {
            if (string.IsNullOrEmpty(_currentRoomId)) return;
            
            if (useBackendServices)
            {
                await LeaveRoomOnBackendAsync();
            }
            else
            {
                await LeaveRoomOnCustomNetworkAsync();
            }
        }
        
        /// <summary>
        /// 在后台服务离开房间
        /// </summary>
        private async Task LeaveRoomOnBackendAsync()
        {
            var tcs = new TaskCompletionSource<bool>();

            _serviceManager.SendPostRequest<object>(ServiceType.Room, $"/rooms/{_currentRoomId}/leave", null, true, response =>
            {
                if (response.IsSuccess)
                {
                    Log($"Left room successfully: {_currentRoomId}");
                }
                else
                {
                    LogError($"Failed to leave room: {response.message}");
                }
                tcs.SetResult(true);
            });

            // 等待请求完成
            await tcs.Task;

            // 通过WebSocket离开房间
            _webSocketManager.LeaveRoom();

            _currentRoomId = null;
            OnRoomLeft?.Invoke(_currentRoomId);
        }
        
        /// <summary>
        /// 在自定义网络离开房间
        /// </summary>
        private async Task LeaveRoomOnCustomNetworkAsync()
        {
            if (_runner != null && _runner.IsRunning)
            {
                await _runner.Shutdown();
            }
            else
            {
                // 如果没有runner，直接完成
                await Task.CompletedTask;
            }

            var oldRoomId = _currentRoomId;
            _currentRoomId = null;
            OnRoomLeft?.Invoke(oldRoomId);
            Log($"Left custom network room: {oldRoomId}");
        }
        
        #region WebSocket事件处理
        
        private void HandleWebSocketConnected()
        {
            Log("WebSocket connected.");
        }
        
        private void HandleWebSocketDisconnected()
        {
            Log("WebSocket disconnected.");
            _isConnected = false;
            OnDisconnectedFromServerEvent?.Invoke("WebSocket连接断开");
        }
        
        private void HandleRoomJoined(string roomId)
        {
            _currentRoomId = roomId;
            OnRoomJoined?.Invoke(roomId);
            Log($"Joined room via WebSocket: {roomId}");
        }
        
        private void HandleRoomLeft(string roomId)
        {
            _currentRoomId = null;
            OnRoomLeft?.Invoke(roomId);
            Log($"Left room via WebSocket: {roomId}");
        }
        
        private void HandleWebSocketError(string error)
        {
            LogError($"WebSocket error: {error}");
            OnErrorOccurred?.Invoke($"WebSocket错误: {error}");
        }
        
        #endregion
        
        #region 认证事件处理
        
        private void HandleAuthStateChanged(bool isAuthenticated)
        {
            Log($"Auth state changed: {(isAuthenticated ? "Authenticated" : "Not Authenticated")}");
            
            if (!isAuthenticated && _isConnected)
            {
                // 认证失效，断开连接
                _webSocketManager.Disconnect();
            }
        }
        
        private void HandleAuthError(string error)
        {
            LogError($"Auth error: {error}");
            OnErrorOccurred?.Invoke($"认证错误: {error}");
        }
        
        #endregion
        
        #region 日志方法
        
        private void Log(string message)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[ImprovedNetworkManager] {message}");
            }
        }
        
        private void LogError(string message)
        {
            Debug.LogError($"[ImprovedNetworkManager] {message}");
        }
        
        #endregion
        
        private void OnDestroy()
        {
            // 清理事件订阅
            if (_webSocketManager != null)
            {
                _webSocketManager.OnConnected -= HandleWebSocketConnected;
                _webSocketManager.OnDisconnected -= HandleWebSocketDisconnected;
                _webSocketManager.OnRoomJoined -= HandleRoomJoined;
                _webSocketManager.OnRoomLeft -= HandleRoomLeft;
                _webSocketManager.OnError -= HandleWebSocketError;
            }
            
            if (_authManager != null)
            {
                _authManager.OnAuthStateChanged -= HandleAuthStateChanged;
                _authManager.OnAuthError -= HandleAuthError;
            }
        }
    }
}
