using UnityEngine;
using CustomNetworking.Core;
using CustomNetworking.Components;
using System;
using System.Collections.Generic;
using GooseDuckKill.Player;

/// <summary>
/// 处理玩家的网络同步和通信
/// </summary>
public class PlayerNetwork : NetworkBehaviour, INetworkRunnerCallbacks
{
    [Header("Network Settings")]
    [SerializeField] private float positionInterpolationFactor = 15f;
    [SerializeField] private float rotationInterpolationFactor = 15f;
    [SerializeField] private int targetFPS = 60;
    
    // 组件引用
    private PlayerController playerController;
    private NetworkRunner runner;
    private NetworkTransform networkTransform;
    
    // 内部状态
    private Vector3 networkPosition;
    private Quaternion networkRotation;
    private bool initialized;
    
    // 事件
    public Action<NetworkRunner> OnNetworkConnected;
    public Action<NetworkRunner> OnNetworkDisconnected;
    public Action<NetworkRunner, PlayerRef> OnPlayerJoinedEvent;
    public Action<NetworkRunner, PlayerRef> OnPlayerLeftEvent;
    
    protected override void Awake()
    {
        base.Awake();

        playerController = GetComponent<PlayerController>();
        networkTransform = GetComponent<NetworkTransform>();
    }
    
    public override void Spawned()
    {
        base.Spawned();
        
        // 获取runner引用
        runner = Object.Runner;
        
        // 初始化位置和旋转
        networkPosition = transform.position;
        networkRotation = transform.rotation;
        
        // 设置目标帧率
        Application.targetFrameRate = targetFPS;
        
        // 注册回调
        if (runner != null)
        {
            runner.AddCallbacks(this);
        }
        
        initialized = true;
    }
    
    public override void Despawned()
    {
        base.Despawned();
        
        // 注销回调
        if (Runner != null)
        {
            Runner.RemoveCallbacks(this);
        }
    }
    
    private void OnDestroy()
    {
        // 确保回调被移除
        if (runner != null)
        {
            runner.RemoveCallbacks(this);
        }
    }
    
    // 发送玩家输入到网络
    public override void FixedUpdateNetwork()
    {
        if (!initialized) return;
        
        // 处理本地玩家的输入
        if (Object.HasInputAuthority && playerController != null)
        {
            // 获取输入并发送到网络
            PlayerNetworkInput input = playerController.GetNextNetworkInput();
            
            // 更新位置和旋转
            if (networkTransform != null && Object.HasStateAuthority)
            {
                // 在Fusion 2.0中，NetworkTransform会自动同步Transform
                // 直接设置位置和旋转，NetworkTransform会处理同步
                transform.position = transform.position;
                transform.rotation = transform.rotation;
            }
        }
        
        // 服务器权威性检查和处理
        if (Object.HasStateAuthority)
        {
            // 在这里可以添加服务器权威性检查，例如防作弊检测
        }
    }
    
    // 在本地Update中平滑处理远程玩家的移动
    private void Update()
    {
        if (!initialized) return;
        
        // 对远程玩家进行插值，本地玩家不需要
        if (!Object.HasInputAuthority)
        {
            // 平滑位置
            transform.position = Vector3.Lerp(
                transform.position,
                networkPosition,
                Time.deltaTime * positionInterpolationFactor
            );
            
            // 平滑旋转
            transform.rotation = Quaternion.Slerp(
                transform.rotation,
                networkRotation,
                Time.deltaTime * rotationInterpolationFactor
            );
        }
    }
    
    #region INetworkRunnerCallbacks 实现
    
    public void OnPlayerJoined(NetworkRunner runner, PlayerRef player)
    {
        // 玩家加入回调
        OnPlayerJoinedEvent?.Invoke(runner, player);
    }
    
    public void OnPlayerLeft(NetworkRunner runner, PlayerRef player)
    {
        // 玩家离开回调
        OnPlayerLeftEvent?.Invoke(runner, player);
    }
    
    public void OnInput(NetworkRunner runner, NetworkInput input)
    {
        // 仅为本地玩家处理输入
        if (Object.HasInputAuthority && playerController != null)
        {
            input.Set(playerController.GetNextNetworkInput());
        }
    }
    
    public void OnInputMissing(NetworkRunner runner, PlayerRef player, NetworkInput input) { }
    
    public void OnConnectedToServer(NetworkRunner runner)
    {
        OnNetworkConnected?.Invoke(runner);
    }
    
    public void OnDisconnectedFromServer(NetworkRunner runner, NetDisconnectReason reason)
    {
        OnNetworkDisconnected?.Invoke(runner);
    }
    
    public void OnConnectRequest(NetworkRunner runner, NetworkRunnerCallbackArgs.ConnectRequest request, byte[] token) { }
    
    public void OnConnectFailed(NetworkRunner runner, NetAddress remoteAddress, NetConnectFailedReason reason) { }
    
    public void OnUserSimulationMessage(NetworkRunner runner, SimulationMessagePtr message) { }
    
    public void OnSessionListUpdated(NetworkRunner runner, List<SessionInfo> sessionList) { }
    
    public void OnCustomAuthenticationResponse(NetworkRunner runner, Dictionary<string, object> data) { }
    
    public void OnHostMigration(NetworkRunner runner, HostMigrationToken hostMigrationToken) { }
    
    public void OnSceneLoadDone(NetworkRunner runner) { }
    
    public void OnSceneLoadStart(NetworkRunner runner) { }
    
    public void OnShutdown(NetworkRunner runner, NetDisconnectReason shutdownReason) { }
    
    public void OnObjectExitAOI(NetworkRunner runner, NetworkObject obj, PlayerRef player) { }
    
    public void OnObjectEnterAOI(NetworkRunner runner, NetworkObject obj, PlayerRef player) { }
    
    public void OnReliableDataReceived(NetworkRunner runner, PlayerRef player, ReliableKey key, ArraySegment<byte> data) { }
    
    public void OnReliableDataProgress(NetworkRunner runner, PlayerRef player, ReliableKey key, float progress) { }
    
    #endregion
} 