using System;
using System.Collections.Generic;
using UnityEngine;
using CustomNetworking.Core;

namespace CustomNetworking.Adapters
{
    /// <summary>
    /// Photon Fusion 适配器 - 提供与原有代码的兼容性
    /// </summary>
    public static class FusionAdapter
    {
        // 类型别名，使现有代码无需修改
        public static class Fusion
        {
            public class NetworkBehaviour : CustomNetworking.Core.NetworkBehaviour { }
            public class NetworkObject : CustomNetworking.Core.NetworkObject { }
            public class NetworkRunner : CustomNetworking.Core.NetworkRunner { }
            
            // 网络属性类型
            public struct NetworkBool : IEquatable<NetworkBool>
            {
                private bool _value;
                
                public NetworkBool(bool value) { _value = value; }
                
                public static implicit operator bool(NetworkBool nb) => nb._value;
                public static implicit operator NetworkBool(bool b) => new NetworkBool(b);
                
                public bool Equals(NetworkBool other) => _value == other._value;
                public override bool Equals(object obj) => obj is NetworkBool other && Equals(other);
                public override int GetHashCode() => _value.GetHashCode();
            }
            
            public struct TickTimer : IEquatable<TickTimer>
            {
                private CustomNetworking.Core.TickTimer _timer;
                
                public TickTimer(CustomNetworking.Core.TickTimer timer) { _timer = timer; }
                
                public bool IsRunning => _timer.IsRunning;
                public bool Expired(NetworkRunner runner) => _timer.Expired(runner);
                public float? RemainingTime(NetworkRunner runner) => _timer.RemainingTime(runner);
                
                public static TickTimer CreateFromSeconds(NetworkRunner runner, float seconds)
                {
                    return new TickTimer(CustomNetworking.Core.TickTimer.CreateFromSeconds(runner, seconds));
                }
                
                public bool Equals(TickTimer other) => _timer.Equals(other._timer);
                public override bool Equals(object obj) => obj is TickTimer other && Equals(other);
                public override int GetHashCode() => _timer.GetHashCode();
            }
            
            public struct PlayerRef : IEquatable<PlayerRef>
            {
                private CustomNetworking.Core.PlayerRef _playerRef;
                
                public PlayerRef(CustomNetworking.Core.PlayerRef playerRef) { _playerRef = playerRef; }
                public PlayerRef(int playerId) { _playerRef = new CustomNetworking.Core.PlayerRef(playerId); }
                
                public int PlayerId => _playerRef.PlayerId;
                public bool IsValid => _playerRef.IsValid;
                
                public static implicit operator CustomNetworking.Core.PlayerRef(PlayerRef pr) => pr._playerRef;
                public static implicit operator PlayerRef(CustomNetworking.Core.PlayerRef pr) => new PlayerRef(pr);
                
                public bool Equals(PlayerRef other) => _playerRef.Equals(other._playerRef);
                public override bool Equals(object obj) => obj is PlayerRef other && Equals(other);
                public override int GetHashCode() => _playerRef.GetHashCode();
                
                public override string ToString() => _playerRef.ToString();
            }
        }
    }
    
    /// <summary>
    /// 网络属性标记 - 兼容 Fusion 的 [Networked] 属性
    /// </summary>
    [AttributeUsage(AttributeTargets.Property)]
    public class NetworkedAttribute : Attribute
    {
        public bool OnChanged { get; set; } = false;
    }
    
    /// <summary>
    /// 兼容的网络输入接口
    /// </summary>
    public interface INetworkInput : CustomNetworking.Core.INetworkInput
    {
        // 继承自自定义框架的接口
    }
    
    /// <summary>
    /// 兼容的网络运行器回调接口
    /// </summary>
    public interface INetworkRunnerCallbacks : CustomNetworking.Core.INetworkRunnerCallbacks
    {
        // 继承自自定义框架的接口
    }
    
    /// <summary>
    /// 网络断开原因枚举
    /// </summary>
    public enum NetDisconnectReason
    {
        None = CustomNetworking.Core.NetDisconnectReason.None,
        Shutdown = CustomNetworking.Core.NetDisconnectReason.Shutdown,
        Timeout = CustomNetworking.Core.NetDisconnectReason.Timeout,
        ConnectionLost = CustomNetworking.Core.NetDisconnectReason.ConnectionLost,
        ServerFull = CustomNetworking.Core.NetDisconnectReason.ServerFull,
        InvalidData = CustomNetworking.Core.NetDisconnectReason.InvalidData,
        Kicked = CustomNetworking.Core.NetDisconnectReason.Kicked,
        Error = CustomNetworking.Core.NetDisconnectReason.Error
    }
    
    /// <summary>
    /// 游戏模式枚举
    /// </summary>
    public enum GameMode
    {
        Server = CustomNetworking.Core.NetworkRunner.GameMode.Server,
        Host = CustomNetworking.Core.NetworkRunner.GameMode.Host,
        Client = CustomNetworking.Core.NetworkRunner.GameMode.Client,
        AutoHostOrClient = CustomNetworking.Core.NetworkRunner.GameMode.AutoHostOrClient,
        Shared = CustomNetworking.Core.NetworkRunner.GameMode.Shared
    }
    
    /// <summary>
    /// 网络地址
    /// </summary>
    public struct NetAddress
    {
        private CustomNetworking.Core.NetAddress _address;
        
        public NetAddress(string address, int port = 0)
        {
            _address = new CustomNetworking.Core.NetAddress(address, port);
        }
        
        public static NetAddress Any(int port = 0)
        {
            return new NetAddress(CustomNetworking.Core.NetAddress.Any(port).ToString(), port);
        }
        
        public static implicit operator CustomNetworking.Core.NetAddress(NetAddress addr) => addr._address;
        public static implicit operator NetAddress(CustomNetworking.Core.NetAddress addr) => new NetAddress(addr.ToString());
        
        public override string ToString() => _address.ToString();
    }
    
    /// <summary>
    /// 启动游戏参数
    /// </summary>
    public class StartGameArgs
    {
        public GameMode GameMode { get; set; }
        public NetAddress Address { get; set; }
        public string SessionName { get; set; }
        public int PlayerCount { get; set; } = 10;
        public bool? IsOpen { get; set; }
        public bool? IsVisible { get; set; }
        
        // 转换为自定义框架的参数
        public CustomNetworking.Core.StartGameArgs ToCustomArgs()
        {
            return new CustomNetworking.Core.StartGameArgs
            {
                GameMode = (CustomNetworking.Core.NetworkRunner.GameMode)GameMode,
                Address = Address,
                SessionName = SessionName,
                MaxPlayers = PlayerCount,
                IsOpen = IsOpen ?? true,
                IsVisible = IsVisible ?? true
            };
        }
    }
    
    /// <summary>
    /// 启动游戏结果
    /// </summary>
    public class StartGameResult
    {
        private CustomNetworking.Core.StartGameResult _result;
        
        public StartGameResult(CustomNetworking.Core.StartGameResult result)
        {
            _result = result;
        }
        
        public bool Ok => _result.Ok;
        public string ShutdownReason => _result.ErrorMessage;
        
        public static implicit operator StartGameResult(CustomNetworking.Core.StartGameResult result)
        {
            return new StartGameResult(result);
        }
    }
    
    /// <summary>
    /// 变化检测器适配器
    /// </summary>
    public class ChangeDetector
    {
        private CustomNetworking.Core.ChangeDetector _detector = new CustomNetworking.Core.ChangeDetector();

        public IEnumerable<string> DetectChanges(object target, out object previousBuffer, out object currentBuffer)
        {
            // 简化实现，实际应该检测对象属性变化
            previousBuffer = null;
            currentBuffer = null;

            // 返回变化的属性名列表
            return new List<string>();
        }

        public bool DetectChange<T>(string key, T currentValue) where T : IEquatable<T>
        {
            return _detector.DetectChange(key, currentValue);
        }

        public void Reset()
        {
            _detector.Reset();
        }
    }
    
    /// <summary>
    /// 属性读取器
    /// </summary>
    public class PropertyReader<T>
    {
        public (T oldValue, T newValue) Read(object previousBuffer, object currentBuffer)
        {
            // 简化实现
            return (default(T), default(T));
        }
    }
    
    /// <summary>
    /// 网络变换组件适配器
    /// </summary>
    public class NetworkTransform : MonoBehaviour
    {
        [SerializeField] private bool _syncPosition = true;
        [SerializeField] private bool _syncRotation = true;
        [SerializeField] private bool _syncScale = false;
        
        private Vector3 _networkPosition;
        private Quaternion _networkRotation;
        private Vector3 _networkScale;
        
        private NetworkObject _networkObject;
        
        private void Awake()
        {
            _networkObject = GetComponent<NetworkObject>();
        }
        
        private void Update()
        {
            if (_networkObject != null && !_networkObject.HasStateAuthority)
            {
                // 插值到网络位置
                if (_syncPosition)
                {
                    transform.position = Vector3.Lerp(transform.position, _networkPosition, Time.deltaTime * 15f);
                }
                
                if (_syncRotation)
                {
                    transform.rotation = Quaternion.Lerp(transform.rotation, _networkRotation, Time.deltaTime * 15f);
                }
                
                if (_syncScale)
                {
                    transform.localScale = Vector3.Lerp(transform.localScale, _networkScale, Time.deltaTime * 15f);
                }
            }
        }
        
        public void SetNetworkPosition(Vector3 position)
        {
            _networkPosition = position;
        }
        
        public void SetNetworkRotation(Quaternion rotation)
        {
            _networkRotation = rotation;
        }
        
        public void SetNetworkScale(Vector3 scale)
        {
            _networkScale = scale;
        }
    }
}

// 全局命名空间别名，使现有代码可以直接使用
namespace Fusion
{
    using CustomNetworking.Adapters;
    using CustomNetworking.Core;
    
    // 导出适配器中的类型到 Fusion 命名空间
    public class NetworkBehaviour : FusionAdapter.Fusion.NetworkBehaviour { }
    public class NetworkObject : FusionAdapter.Fusion.NetworkObject { }
    public class NetworkRunner : FusionAdapter.Fusion.NetworkRunner { }
    public class NetworkTransform : CustomNetworking.Components.NetworkTransform { }
    
    public struct NetworkBool : IEquatable<NetworkBool>
    {
        private FusionAdapter.Fusion.NetworkBool _value;
        
        public NetworkBool(bool value) { _value = new FusionAdapter.Fusion.NetworkBool(value); }
        
        public static implicit operator bool(NetworkBool nb) => nb._value;
        public static implicit operator NetworkBool(bool b) => new NetworkBool(b);
        
        public bool Equals(NetworkBool other) => _value.Equals(other._value);
        public override bool Equals(object obj) => obj is NetworkBool other && Equals(other);
        public override int GetHashCode() => _value.GetHashCode();
    }
    
    public struct TickTimer : IEquatable<TickTimer>
    {
        private FusionAdapter.Fusion.TickTimer _timer;
        
        public TickTimer(FusionAdapter.Fusion.TickTimer timer) { _timer = timer; }
        
        public bool IsRunning => _timer.IsRunning;
        public bool Expired(NetworkRunner runner) => _timer.Expired(runner);
        public float? RemainingTime(NetworkRunner runner) => _timer.RemainingTime(runner);
        
        public static TickTimer CreateFromSeconds(NetworkRunner runner, float seconds)
        {
            return new TickTimer(FusionAdapter.Fusion.TickTimer.CreateFromSeconds(runner, seconds));
        }
        
        public bool Equals(TickTimer other) => _timer.Equals(other._timer);
        public override bool Equals(object obj) => obj is TickTimer other && Equals(other);
        public override int GetHashCode() => _timer.GetHashCode();
    }
    
    public struct PlayerRef : IEquatable<PlayerRef>
    {
        private FusionAdapter.Fusion.PlayerRef _playerRef;
        
        public PlayerRef(int playerId) { _playerRef = new FusionAdapter.Fusion.PlayerRef(playerId); }
        
        public int PlayerId => _playerRef.PlayerId;
        public bool IsValid => _playerRef.IsValid;
        
        public bool Equals(PlayerRef other) => _playerRef.Equals(other._playerRef);
        public override bool Equals(object obj) => obj is PlayerRef other && Equals(other);
        public override int GetHashCode() => _playerRef.GetHashCode();
        
        public override string ToString() => _playerRef.ToString();
    }
    
    // 其他类型别名
    public interface INetworkInput : CustomNetworking.Core.INetworkInput { }
    public interface INetworkRunnerCallbacks : CustomNetworking.Core.INetworkRunnerCallbacks { }
    
    public enum NetDisconnectReason
    {
        None = CustomNetworking.Core.NetDisconnectReason.None,
        Shutdown = CustomNetworking.Core.NetDisconnectReason.Shutdown,
        Timeout = CustomNetworking.Core.NetDisconnectReason.Timeout,
        ConnectionLost = CustomNetworking.Core.NetDisconnectReason.ConnectionLost,
        ServerFull = CustomNetworking.Core.NetDisconnectReason.ServerFull,
        InvalidData = CustomNetworking.Core.NetDisconnectReason.InvalidData,
        Kicked = CustomNetworking.Core.NetDisconnectReason.Kicked,
        Error = CustomNetworking.Core.NetDisconnectReason.Error
    }
    
    public enum GameMode
    {
        Server = CustomNetworking.Core.NetworkRunner.GameMode.Server,
        Host = CustomNetworking.Core.NetworkRunner.GameMode.Host,
        Client = CustomNetworking.Core.NetworkRunner.GameMode.Client,
        AutoHostOrClient = CustomNetworking.Core.NetworkRunner.GameMode.AutoHostOrClient,
        Shared = CustomNetworking.Core.NetworkRunner.GameMode.Shared
    }
    
    public struct NetAddress
    {
        private CustomNetworking.Core.NetAddress _address;

        public NetAddress(string address, int port = 0) { _address = new CustomNetworking.Core.NetAddress(address, port); }

        public static NetAddress Any(int port = 0) => new NetAddress { _address = CustomNetworking.Core.NetAddress.Any(port) };

        public override string ToString() => _address.ToString();
    }
    
    public class StartGameArgs : CustomNetworking.Core.StartGameArgs { }
    public class StartGameResult : CustomNetworking.Core.StartGameResult
    {
        public StartGameResult(bool ok, string errorMessage = null) : base(ok, errorMessage) { }
    }

    public class ChangeDetector : CustomNetworking.Core.ChangeDetector { }

    // 属性标记
    public class NetworkedAttribute : CustomNetworking.Core.NetworkedAttribute { }
}
