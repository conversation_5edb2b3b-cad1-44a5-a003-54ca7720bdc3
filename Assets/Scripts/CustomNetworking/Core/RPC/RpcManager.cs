using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using UnityEngine;
using CustomNetworking.Optimization;

namespace CustomNetworking.Core.RPC
{
    /// <summary>
    /// RPC 管理器 - 负责 RPC 方法的注册、调用和消息处理
    /// </summary>
    public class RpcManager
    {
        #region 单例
        
        private static RpcManager _instance;
        public static RpcManager Instance
        {
            get
            {
                if (_instance == null)
                    _instance = new RpcManager();
                return _instance;
            }
        }
        
        #endregion
        
        #region 属性和字段
        
        /// <summary>
        /// 注册的 RPC 方法字典 [ObjectId -> [MethodHash -> RpcMethodInfo]]
        /// </summary>
        private Dictionary<NetworkId, Dictionary<int, RpcMethodInfo>> _registeredMethods = 
            new Dictionary<NetworkId, Dictionary<int, RpcMethodInfo>>();
        
        /// <summary>
        /// 待发送的 RPC 消息队列
        /// </summary>
        private Queue<RpcMessage> _outgoingMessages = new Queue<RpcMessage>();
        
        /// <summary>
        /// 待处理的 RPC 消息队列
        /// </summary>
        private Queue<RpcMessage> _incomingMessages = new Queue<RpcMessage>();
        
        /// <summary>
        /// 网络运行器引用
        /// </summary>
        private NetworkRunner _runner;
        
        #endregion
        
        #region 初始化
        
        private RpcManager()
        {
        }
        
        /// <summary>
        /// 初始化 RPC 管理器
        /// </summary>
        public void Initialize(NetworkRunner runner)
        {
            _runner = runner;
            UnityEngine.Debug.Log("RpcManager initialized");
        }
        
        /// <summary>
        /// 清理 RPC 管理器
        /// </summary>
        public void Cleanup()
        {
            _registeredMethods.Clear();
            _outgoingMessages.Clear();
            _incomingMessages.Clear();
            _runner = null;
            UnityEngine.Debug.Log("RpcManager cleaned up");
        }
        
        #endregion
        
        #region RPC 方法注册
        
        /// <summary>
        /// 注册网络行为的 RPC 方法
        /// </summary>
        public void RegisterNetworkBehaviour(NetworkBehaviour behaviour)
        {
            if (behaviour?.Object == null)
                return;
                
            var objectId = behaviour.Object.Id;
            if (!_registeredMethods.ContainsKey(objectId))
            {
                _registeredMethods[objectId] = new Dictionary<int, RpcMethodInfo>();
            }
            
            var methods = behaviour.GetType().GetMethods(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
            
            foreach (var method in methods)
            {
                var rpcAttribute = method.GetCustomAttribute<RpcAttribute>();
                if (rpcAttribute != null)
                {
                    var parameterTypes = method.GetParameters().Select(p => p.ParameterType).ToArray();
                    var rpcInfo = new RpcInfo(method.Name, rpcAttribute, parameterTypes);
                    
                    var methodInfo = new RpcMethodInfo
                    {
                        Method = method,
                        Target = behaviour,
                        RpcInfo = rpcInfo
                    };
                    
                    _registeredMethods[objectId][rpcInfo.MethodHash] = methodInfo;

                    UnityEngine.Debug.Log($"Registered RPC method: {behaviour.GetType().Name}.{method.Name} " +
                             $"(Hash: {rpcInfo.MethodHash}) for object {objectId}");
                }
            }
        }
        
        /// <summary>
        /// 取消注册网络行为的 RPC 方法
        /// </summary>
        public void UnregisterNetworkBehaviour(NetworkBehaviour behaviour)
        {
            if (behaviour?.Object == null)
                return;
                
            var objectId = behaviour.Object.Id;
            if (_registeredMethods.ContainsKey(objectId))
            {
                _registeredMethods.Remove(objectId);
                UnityEngine.Debug.Log($"Unregistered RPC methods for object {objectId}");
            }
        }
        
        #endregion
        
        #region RPC 调用
        
        /// <summary>
        /// 发送 RPC 调用
        /// </summary>
        public void SendRpc(NetworkBehaviour sender, string methodName, RpcTargets target,
                           bool reliable, RpcPriority priority, PlayerRef targetPlayer, bool includeSender, params object[] parameters)
        {
            if (sender?.Object == null || _runner == null)
            {
                UnityEngine.Debug.LogWarning("Cannot send RPC: sender or runner is null");
                return;
            }

            var objectId = sender.Object.Id;

            // 查找方法信息
            if (!_registeredMethods.ContainsKey(objectId))
            {
                UnityEngine.Debug.LogWarning($"No RPC methods registered for object {objectId}");
                return;
            }

            var methodDict = _registeredMethods[objectId];
            var methodInfo = methodDict.Values.FirstOrDefault(m => m.RpcInfo.MethodName == methodName);

            if (methodInfo == null)
            {
                UnityEngine.Debug.LogWarning($"RPC method '{methodName}' not found for object {objectId}");
                return;
            }

            // 序列化参数
            byte[] parameterData = RpcParameterSerializer.SerializeParameters(parameters);

            // 带宽优化检查
            var optimizer = NetworkBandwidthOptimizer.Instance;
            if (optimizer != null && !optimizer.CanSendRpc(sender, parameterData.Length))
            {
                // 根据优先级决定是否丢弃或延迟
                if (priority == RpcPriority.Low)
                {
                    UnityEngine.Debug.LogWarning($"RPC '{methodName}' dropped due to bandwidth optimization");
                    return;
                }
                else if (priority == RpcPriority.Normal)
                {
                    // 延迟发送
                    DelayRpcSend(sender, methodName, target, reliable, priority, targetPlayer, includeSender, parameters);
                    return;
                }
                // High priority RPC 强制发送
            }

            // 创建 RPC 消息
            var message = new RpcMessage(
                _runner.LocalPlayer,
                objectId,
                methodInfo.RpcInfo.MethodHash,
                methodName,
                target,
                reliable,
                priority,
                parameterData,
                targetPlayer,
                includeSender
            );

            // 记录带宽使用
            if (optimizer != null)
            {
                optimizer.RecordRpcSent(sender, parameterData.Length);
            }

            // 添加到发送队列
            _outgoingMessages.Enqueue(message);

            UnityEngine.Debug.Log($"Queued RPC: {message}");
        }

        /// <summary>
        /// 延迟发送 RPC
        /// </summary>
        private void DelayRpcSend(NetworkBehaviour sender, string methodName, RpcTargets target,
                                 bool reliable, RpcPriority priority, PlayerRef targetPlayer, bool includeSender, params object[] parameters)
        {
            // 简单的延迟实现 - 可以改进为更复杂的队列系统
            UnityEngine.Object.FindObjectOfType<MonoBehaviour>()?.StartCoroutine(DelayedSendCoroutine(
                sender, methodName, target, reliable, priority, targetPlayer, includeSender, parameters));
        }

        /// <summary>
        /// 延迟发送协程
        /// </summary>
        private System.Collections.IEnumerator DelayedSendCoroutine(NetworkBehaviour sender, string methodName, RpcTargets target,
                                                                    bool reliable, RpcPriority priority, PlayerRef targetPlayer, bool includeSender, params object[] parameters)
        {
            yield return new WaitForSeconds(0.1f); // 延迟100ms
            SendRpc(sender, methodName, target, reliable, priority, targetPlayer, includeSender, parameters);
        }
        
        #endregion
        
        #region 消息处理
        
        /// <summary>
        /// 处理传入的 RPC 消息
        /// </summary>
        public void HandleIncomingRpcMessage(RpcMessage message)
        {
            if (message == null)
                return;
                
            _incomingMessages.Enqueue(message);
        }
        
        /// <summary>
        /// 更新 RPC 管理器 - 处理消息队列
        /// </summary>
        public void Update()
        {
            ProcessOutgoingMessages();
            ProcessIncomingMessages();
        }
        
        /// <summary>
        /// 处理发送消息队列
        /// </summary>
        private void ProcessOutgoingMessages()
        {
            while (_outgoingMessages.Count > 0)
            {
                var message = _outgoingMessages.Dequeue();
                SendRpcMessage(message);
            }
        }
        
        /// <summary>
        /// 处理接收消息队列
        /// </summary>
        private void ProcessIncomingMessages()
        {
            while (_incomingMessages.Count > 0)
            {
                var message = _incomingMessages.Dequeue();
                ExecuteRpcMessage(message);
            }
        }
        
        /// <summary>
        /// 发送 RPC 消息到网络
        /// </summary>
        private void SendRpcMessage(RpcMessage message)
        {
            if (_runner == null)
                return;
                
            // 根据目标类型发送消息
            switch (message.Target)
            {
                case RpcTargets.All:
                    _runner.SendToAll(message);
                    if (message.IncludeSender)
                        ExecuteRpcMessage(message); // 本地也执行
                    break;
                    
                case RpcTargets.Others:
                    _runner.SendToAll(message);
                    break;
                    
                case RpcTargets.Server:
                    if (_runner.IsServer)
                        ExecuteRpcMessage(message); // 本地执行
                    else
                        _runner.SendToServer(message);
                    break;
                    
                case RpcTargets.Player:
                    if (message.TargetPlayer.Equals(_runner.LocalPlayer))
                        ExecuteRpcMessage(message); // 本地执行
                    else
                        _runner.SendToPlayer(message.TargetPlayer, message);
                    break;
                    
                case RpcTargets.StateAuthority:
                case RpcTargets.InputAuthority:
                    // 这些需要根据对象的权限来确定目标
                    _runner.SendToAll(message);
                    break;
            }
        }
        
        /// <summary>
        /// 执行 RPC 消息
        /// </summary>
        private void ExecuteRpcMessage(RpcMessage message)
        {
            try
            {
                // 查找目标对象和方法
                if (!_registeredMethods.ContainsKey(message.TargetObjectId))
                {
                    UnityEngine.Debug.LogWarning($"No RPC methods registered for object {message.TargetObjectId}");
                    return;
                }
                
                var methodDict = _registeredMethods[message.TargetObjectId];
                if (!methodDict.ContainsKey(message.MethodHash))
                {
                    UnityEngine.Debug.LogWarning($"RPC method with hash {message.MethodHash} not found for object {message.TargetObjectId}");
                    return;
                }
                
                var methodInfo = methodDict[message.MethodHash];
                
                // 反序列化参数
                object[] parameters = RpcParameterSerializer.DeserializeParameters(
                    message.ParameterData, 
                    methodInfo.RpcInfo.ParameterTypes);
                
                // 调用方法
                methodInfo.Method.Invoke(methodInfo.Target, parameters);

                UnityEngine.Debug.Log($"Executed RPC: {message.MethodName} on object {message.TargetObjectId}");
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"Error executing RPC {message.MethodName}: {ex}");
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// RPC 方法信息
    /// </summary>
    internal class RpcMethodInfo
    {
        public MethodInfo Method { get; set; }
        public NetworkBehaviour Target { get; set; }
        public RpcInfo RpcInfo { get; set; }
    }
}
